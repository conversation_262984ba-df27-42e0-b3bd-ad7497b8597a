import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        AccountStorage st;
        System.out.println("1.Mem 2.File:");
        int op = sc.nextInt();
        if (op == 1) {
            st = new MemoryAccountStorage();
        } else {
            st = new FileAccountStorage();
        }

        ATM atm = new ATM(st);

        System.out.print("ID: ");
        String id = sc.next();
        atm.lg(id);

        while (true) {
            System.out.println("1.Q 2.D 3.W 4.E");
            int c = sc.nextInt();
            if (c == 1) {
                System.out.println("B: " + atm.gb());
            } else if (c == 2) {
                System.out.print("Amt: ");
                double a = sc.nextDouble();
                atm.dp(a);
            } else if (c == 3) {
                System.out.print("Amt: ");
                double a = sc.nextDouble();
                try {
                    atm.wd(a);
                } catch (LowBalanceException e) {
                    System.out.println("Low");
                }
            } else if (c == 4) {
                atm.qt();
                break;
            }
        }

        sc.close();
    }
}
