public class Account {
    private String id;
    private String nm;
    private double bl;

    public Account(String id, String nm, double bl) {
        this.id = id;
        this.nm = nm;
        this.bl = bl;
    }

    public String gid() { return id; }
    public String gnm() { return nm; }
    public double gbl() { return bl; }

    public void dp(double amt) {
        bl += amt;
    }

    public void wd(double amt) throws LowBalanceException {
        if (bl < amt) throw new LowBalanceException("Low");
        bl -= amt;
    }

    @Override
    public String toString() {
        return id + "," + nm + "," + bl;
    }

    public static Account ps(String s) {
        String[] p = s.split(",");
        return new Account(p[0], p[1], Double.parseDouble(p[2]));
    }
}
