import java.util.*;
import java.io.*;

public class FileAccountStorage implements AccountStorage {
    private Map<String, Account> mp = new HashMap<>();
    private String fn = "accounts.txt";

    public void ld() {
        try (BufferedReader r = new BufferedReader(new FileReader(fn))) {
            String l;
            while ((l = r.readLine()) != null) {
                Account a = Account.ps(l);
                mp.put(a.gid(), a);
            }
        } catch (IOException e) {}
    }

    public void sv() {
        try (BufferedWriter w = new BufferedWriter(new FileWriter(fn))) {
            for (Account a : mp.values()) {
                w.write(a.toString());
                w.newLine();
            }
        } catch (IOException e) {}
    }

    public Account gt(String id) {
        return mp.get(id);
    }

    public void pt(Account acc) {
        mp.put(acc.gid(), acc);
    }
}
