public class ATM {
    private AccountStorage st;
    private Account cu;

    public ATM(AccountStorage st) {
        this.st = st;
        st.ld();
    }

    public void lg(String id) {
        cu = st.gt(id);
        if (cu == null) {
            cu = new Account(id, "U" + id, 0);
            st.pt(cu);
        }
    }

    public void dp(double amt) {
        cu.dp(amt);
    }

    public void wd(double amt) throws LowBalanceException {
        cu.wd(amt);
    }

    public double gb() {
        return cu.gbl();
    }

    public void qt() {
        st.pt(cu);
        st.sv();
    }
}
